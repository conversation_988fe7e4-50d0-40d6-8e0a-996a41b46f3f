@echo off
setlocal enabledelayedexpansion

echo ========================================
echo   若依Vue Pro - 一键启动
echo ========================================

:: Get script directory
set "SCRIPT_DIR=%~dp0"
set "DEPLOY_ROOT=%SCRIPT_DIR%"

echo 部署目录: %DEPLOY_ROOT%
echo.

:: Environment check
echo ========================================
echo   环境检查
echo ========================================

:: Check Java
echo [1/5] 检查Java环境...
java -version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Java环境
    echo 请安装Java 17或更高版本
    pause
    exit /b 1
) else (
    echo 成功: Java环境已找到
)

:: Check backend files
echo.
echo [2/5] 检查后端文件...
if not exist "%DEPLOY_ROOT%backend\yudao-server.jar" (
    echo 错误: 后端JAR文件未找到
    pause
    exit /b 1
) else (
    echo 成功: 后端JAR文件已找到
)

:: Check frontend files
echo.
echo [3/5] 检查前端文件...
if not exist "%DEPLOY_ROOT%frontend\dist\index.html" (
    echo 错误: 前端文件未找到
    pause
    exit /b 1
) else (
    echo 成功: 前端文件已找到
)

:: Check config files
echo.
echo [4/5] 检查配置文件...
if not exist "%DEPLOY_ROOT%config\application-local.yaml" (
    echo 错误: 后端配置文件未找到
    pause
    exit /b 1
) else (
    echo 成功: 后端配置文件已找到
)

if not exist "%DEPLOY_ROOT%config\nginx.conf" (
    echo 错误: Nginx配置模板未找到
    pause
    exit /b 1
) else (
    echo 成功: Nginx配置模板已找到
)

:: Generate Nginx config
echo.
echo [5/5] 生成Nginx配置...
set "FRONTEND_PATH=%DEPLOY_ROOT%frontend\dist"
set "FRONTEND_PATH=!FRONTEND_PATH:\=/!"

copy "%DEPLOY_ROOT%config\nginx-clean.conf" "%DEPLOY_ROOT%frontend\nginx-1.28.0\conf\nginx.conf" >nul
powershell -Command "(Get-Content '%DEPLOY_ROOT%frontend\nginx-1.28.0\conf\nginx.conf') -replace '{{FRONTEND_PATH}}', '!FRONTEND_PATH!' | Set-Content '%DEPLOY_ROOT%frontend\nginx-1.28.0\conf\nginx.conf'"
echo 成功: Nginx配置已生成

echo.
echo ========================================
echo   启动服务
echo ========================================

:: Start frontend service
echo.
echo [1/2] 启动前端服务 (Nginx)...
cd /d "%DEPLOY_ROOT%frontend\nginx-1.28.0"
nginx.exe -s quit >nul 2>&1
start "Nginx Server" nginx.exe
timeout /t 2 >nul

netstat -an | findstr ":80 " >nul 2>&1
if not errorlevel 1 (
    echo 成功: 前端服务已启动 - http://localhost
) else (
    echo 警告: 前端服务可能未正常启动
)

:: Start backend service
echo.
echo [2/2] 启动后端服务 (Spring Boot)...
echo 这可能需要30-60秒，请稍候...
cd /d "%DEPLOY_ROOT%backend"
start "Yudao Backend Server" java -jar yudao-server.jar --spring.config.additional-location=file:../config/

echo 信息: 后端服务正在启动 - http://localhost:48080

echo.
echo ========================================
echo   启动完成
echo ========================================
echo.
echo 前端地址: http://localhost
echo 后端API: http://localhost:48080
echo API文档: http://localhost:48080/doc.html
echo 默认登录: admin / admin123
echo.
echo 重要提示:
echo 1. 后端服务需要30-60秒才能完全启动
echo 2. 请等待后端启动完成后再访问前端
echo 3. 可通过任务管理器或控制台窗口停止服务
echo.
echo 服务管理:
echo - 停止Nginx: nginx -s quit
echo - 停止后端: 关闭Java控制台窗口
echo.

:: Wait 10 seconds then open browser
echo 10秒后打开浏览器...
timeout /t 10 >nul
start http://localhost

pause
