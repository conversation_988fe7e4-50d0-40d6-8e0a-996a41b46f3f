@echo off
setlocal enabledelayedexpansion

:: ===========================================
:: 若依Vue Pro - 一键启动脚本
:: 自动启动前后端服务，支持双击运行
:: ===========================================

title 若依Vue Pro - 一键启动

echo.
echo ========================================
echo   若依Vue Pro - 一键启动
echo ========================================
echo   版本: 1.0.0
echo   环境: 本地开发环境
echo ========================================
echo.

:: 获取脚本所在目录
set "SCRIPT_DIR=%~dp0"
set "DEPLOY_ROOT=%SCRIPT_DIR%"

echo [信息] 部署目录: %DEPLOY_ROOT%
echo.

:: 全面环境检查
echo ========================================
echo   环境检查
echo ========================================

:: 检查Java环境
echo [1/7] 检查Java环境...
java -version >nul 2>&1
if errorlevel 1 (
    echo [错误] 未找到Java环境
    echo [提示] 请安装Java 17或更高版本
    echo [下载] https://www.oracle.com/java/technologies/downloads/
    echo.
    pause
    exit /b 1
) else (
    for /f "tokens=3" %%g in ('java -version 2^>^&1 ^| findstr /i "version"') do (
        set JAVA_VERSION=%%g
        set JAVA_VERSION=!JAVA_VERSION:"=!
    )
    echo [成功] Java环境: !JAVA_VERSION!
)

:: 检查后端文件
echo.
echo [2/7] 检查后端文件...
if not exist "%DEPLOY_ROOT%backend\yudao-server.jar" (
    echo [错误] 后端JAR文件未找到
    echo [路径] %DEPLOY_ROOT%backend\yudao-server.jar
    pause
    exit /b 1
) else (
    echo [成功] 后端JAR文件存在
)

:: 检查前端文件
echo.
echo [3/7] 检查前端文件...
if not exist "%DEPLOY_ROOT%frontend\dist\index.html" (
    echo [错误] 前端静态文件未找到
    echo [路径] %DEPLOY_ROOT%frontend\dist\index.html
    pause
    exit /b 1
) else (
    echo [成功] 前端静态文件存在
)

:: 检查配置文件
echo.
echo [4/7] 检查配置文件...
if not exist "%DEPLOY_ROOT%config\application-local.yaml" (
    echo [错误] 后端配置文件未找到
    echo [路径] %DEPLOY_ROOT%config\application-local.yaml
    pause
    exit /b 1
) else (
    echo [成功] 后端配置文件存在
)

if not exist "%DEPLOY_ROOT%config\nginx.conf" (
    echo [错误] Nginx配置模板未找到
    echo [路径] %DEPLOY_ROOT%config\nginx.conf
    pause
    exit /b 1
) else (
    echo [成功] Nginx配置模板存在
)

:: 检查Nginx程序
echo.
echo [5/7] 检查Nginx程序...
if not exist "%DEPLOY_ROOT%frontend\nginx-1.28.0\nginx.exe" (
    echo [错误] Nginx程序未找到
    echo [路径] %DEPLOY_ROOT%frontend\nginx-1.28.0\nginx.exe
    pause
    exit /b 1
) else (
    echo [成功] Nginx程序存在
)

:: 检查端口占用
echo.
echo [6/7] 检查端口占用...
set "PORT_CONFLICT=0"

netstat -an | findstr ":48080" >nul 2>&1
if not errorlevel 1 (
    echo [警告] 后端端口48080已被占用
    set "PORT_CONFLICT=1"
) else (
    echo [成功] 后端端口48080可用
)

netstat -an | findstr ":80 " >nul 2>&1
if not errorlevel 1 (
    echo [警告] 前端端口80已被占用
    set "PORT_CONFLICT=1"
) else (
    echo [成功] 前端端口80可用
)

if "!PORT_CONFLICT!"=="1" (
    echo.
    echo [提示] 发现端口冲突，是否继续启动？
    set /p CONTINUE="继续启动 (y/N): "
    if /i not "!CONTINUE!"=="y" (
        echo [取消] 用户取消启动
        pause
        exit /b 1
    )
)

:: 生成Nginx配置
echo.
echo [7/7] 生成Nginx配置...
set "FRONTEND_PATH=%DEPLOY_ROOT%frontend\dist"
set "FRONTEND_PATH=!FRONTEND_PATH:\=/!"

:: 使用nginx-clean.conf模板，与simple版本保持一致
copy "%DEPLOY_ROOT%config\nginx-clean.conf" "%DEPLOY_ROOT%frontend\nginx-1.28.0\conf\nginx.conf" >nul 2>&1
if errorlevel 1 (
    echo [错误] 无法复制Nginx配置模板
    pause
    exit /b 1
)

:: 使用PowerShell进行模板替换（与simple版本保持一致）
powershell -Command "(Get-Content '%DEPLOY_ROOT%frontend\nginx-1.28.0\conf\nginx.conf') -replace '{{FRONTEND_PATH}}', '!FRONTEND_PATH!' | Set-Content '%DEPLOY_ROOT%frontend\nginx-1.28.0\conf\nginx.conf'"

echo [成功] Nginx配置生成完成

echo.
echo ========================================
echo   启动服务
echo ========================================

:: 启动前端服务
echo.
echo [1/2] 启动前端服务 (Nginx)...
cd /d "%DEPLOY_ROOT%frontend\nginx-1.28.0"
nginx.exe -s quit >nul 2>&1
start "Nginx Server" nginx.exe
timeout /t 2 >nul

netstat -an | findstr ":80 " >nul 2>&1
if not errorlevel 1 (
    echo [成功] 前端服务启动成功 - http://localhost
) else (
    echo [错误] 前端服务启动失败
)

:: 启动后端服务
echo.
echo [2/2] 启动后端服务 (Spring Boot)...
echo [提示] 后端启动需要30-60秒，请耐心等待...
cd /d "%DEPLOY_ROOT%backend"
start "Yudao Backend Server" java -jar yudao-server.jar --spring.config.additional-location=file:../config/

echo [信息] 后端服务启动中 - http://localhost:48080

echo.
echo ========================================
echo   启动完成
echo ========================================
echo.
echo [前端地址] http://localhost
echo [后端API]  http://localhost:48080
echo [API文档]  http://localhost:48080/doc.html
echo [默认账号] admin / admin123
echo.
echo [重要提示]
echo 1. 后端服务需要30-60秒完全启动
echo 2. 请等待后端启动完成后再访问前端
echo 3. 可通过任务管理器或控制台窗口停止服务
echo.
echo [服务管理]
echo - 停止Nginx: nginx -s quit
echo - 停止后端: 关闭Java控制台窗口
echo.

:: 等待10秒后自动打开浏览器
echo 10秒后将自动打开浏览器...
timeout /t 10 >nul
start http://localhost

pause
