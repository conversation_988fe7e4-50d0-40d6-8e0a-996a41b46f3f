@echo off
setlocal enabledelayedexpansion

echo ========================================
echo   若依Vue Pro - 后端服务
echo ========================================

:: Get script directory
set "SCRIPT_DIR=%~dp0"
set "DEPLOY_ROOT=%SCRIPT_DIR%"

echo 部署目录: %DEPLOY_ROOT%
echo.

:: Check Java environment
echo [1/4] 检查Java环境...
java -version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Java环境
    echo 请安装Java 17或更高版本
    pause
    exit /b 1
) else (
    for /f "tokens=3" %%g in ('java -version 2^>^&1 ^| findstr /i "version"') do (
        set JAVA_VERSION=%%g
        set JAVA_VERSION=!JAVA_VERSION:"=!
    )
    echo 成功: Java环境已找到: !JAVA_VERSION!
)

:: Check JAR file
echo.
echo [2/4] 检查后端JAR文件...
if not exist "%DEPLOY_ROOT%backend\yudao-server.jar" (
    echo 错误: 后端JAR文件未找到
    echo 路径: %DEPLOY_ROOT%backend\yudao-server.jar
    pause
    exit /b 1
) else (
    for %%A in ("%DEPLOY_ROOT%backend\yudao-server.jar") do (
        set JAR_SIZE=%%~zA
        set /a JAR_SIZE_MB=!JAR_SIZE!/1024/1024
    )
    echo 成功: 后端JAR文件已找到 (!JAR_SIZE_MB! MB)
)

:: Check config file
echo.
echo [3/4] 检查配置文件...
if not exist "%DEPLOY_ROOT%config\application-local.yaml" (
    echo 错误: 配置文件未找到
    echo 路径: %DEPLOY_ROOT%config\application-local.yaml
    pause
    exit /b 1
) else (
    echo 成功: 配置文件已找到
)

:: Check port
echo.
echo [4/4] 检查端口48080...
netstat -an | findstr ":48080" >nul 2>&1
if not errorlevel 1 (
    echo 警告: 端口48080已被占用
    set /p CONTINUE="是否继续? (y/N): "
    if /i not "!CONTINUE!"=="y" (
        echo 用户取消操作
        pause
        exit /b 1
    )
) else (
    echo 成功: 端口48080可用
)

:: Start backend service
echo.
echo ========================================
echo   启动后端服务...
echo ========================================
echo.
echo 后端地址: http://localhost:48080
echo 管理API: http://localhost:48080/admin-api
echo API文档: http://localhost:48080/doc.html
echo 默认登录: admin / admin123
echo.
echo 正在启动... (这可能需要30-60秒)
echo 按Ctrl+C停止服务
echo.

:: Change to backend directory and start
cd /d "%DEPLOY_ROOT%backend"
java -jar yudao-server.jar --spring.config.additional-location=file:../config/

echo.
echo 服务已停止
pause
