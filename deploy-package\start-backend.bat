@echo off
setlocal enabledelayedexpansion

:: ===========================================
:: 若依Vue Pro - 后端服务启动脚本
:: 支持双击启动，自动环境检测
:: ===========================================

title 若依Vue Pro - 后端服务启动

echo.
echo ========================================
echo   若依Vue Pro - 后端服务启动
echo ========================================
echo.

:: 获取脚本所在目录
set "SCRIPT_DIR=%~dp0"
set "DEPLOY_ROOT=%SCRIPT_DIR%"

echo [信息] 部署目录: %DEPLOY_ROOT%
echo.

:: 环境检查
echo [1/5] 检查Java环境...
java -version >nul 2>&1
if errorlevel 1 (
    echo [错误] 未找到Java环境，请安装Java 17或更高版本
    echo [提示] 下载地址: https://www.oracle.com/java/technologies/downloads/
    echo.
    pause
    exit /b 1
) else (
    echo [成功] Java环境检测成功
)

:: 检查JAR文件
echo.
echo [2/5] 检查后端JAR文件...
if not exist "%DEPLOY_ROOT%backend\yudao-server.jar" (
    echo [错误] 后端JAR文件未找到
    echo [路径] %DEPLOY_ROOT%backend\yudao-server.jar
    echo [提示] 请确保已完成项目编译
    echo.
    pause
    exit /b 1
) else (
    echo [成功] 后端JAR文件检测成功
)

:: 检查配置文件
echo.
echo [3/5] 检查配置文件...
if not exist "%DEPLOY_ROOT%config\application-local.yaml" (
    echo [错误] 配置文件未找到
    echo [路径] %DEPLOY_ROOT%config\application-local.yaml
    echo [提示] 请确保配置文件存在
    echo.
    pause
    exit /b 1
) else (
    echo [成功] 配置文件检测成功
)

:: 检查端口占用
echo.
echo [4/5] 检查端口占用...
netstat -an | findstr ":48080" >nul 2>&1
if not errorlevel 1 (
    echo [警告] 端口48080已被占用
    echo [提示] 请停止占用该端口的服务，或修改配置文件中的端口号
    echo.
    set /p CONTINUE="是否继续启动? (y/N): "
    if /i not "!CONTINUE!"=="y" (
        echo [取消] 用户取消启动
        pause
        exit /b 1
    )
) else (
    echo [成功] 端口48080可用
)

:: 启动服务
echo.
echo [5/5] 启动后端服务...
echo ========================================
echo   服务启动中，请稍候...
echo ========================================
echo.
echo [访问地址] http://localhost:48080
echo [管理API]  http://localhost:48080/admin-api
echo [API文档]  http://localhost:48080/doc.html
echo [默认账号] admin / admin123
echo.
echo [提示] 启动可能需要30-60秒，请耐心等待
echo [提示] 按 Ctrl+C 可停止服务
echo.

:: 切换到后端目录并启动
cd /d "%DEPLOY_ROOT%backend"
java -jar yudao-server.jar --spring.config.additional-location=file:../config/

echo.
echo [信息] 服务已停止
pause
