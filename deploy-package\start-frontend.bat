@echo off
setlocal enabledelayedexpansion

echo ========================================
echo   若依Vue Pro - 前端服务
echo ========================================

:: Get script directory
set "SCRIPT_DIR=%~dp0"
set "DEPLOY_ROOT=%SCRIPT_DIR%"

echo 部署目录: %DEPLOY_ROOT%
echo.

:: Check Nginx program
echo [1/4] 检查Nginx程序...
if not exist "%DEPLOY_ROOT%frontend\nginx-1.28.0\nginx.exe" (
    echo 错误: Nginx程序未找到
    echo 路径: %DEPLOY_ROOT%frontend\nginx-1.28.0\nginx.exe
    pause
    exit /b 1
) else (
    echo 成功: Nginx程序已找到
)

:: Check frontend files
echo.
echo [2/4] 检查前端文件...
if not exist "%DEPLOY_ROOT%frontend\dist\index.html" (
    echo 错误: 前端文件未找到
    echo 路径: %DEPLOY_ROOT%frontend\dist\index.html
    pause
    exit /b 1
) else (
    echo 成功: 前端文件已找到
)

:: Generate Nginx config
echo.
echo [3/4] 生成Nginx配置...
set "FRONTEND_PATH=%DEPLOY_ROOT%frontend\dist"
set "FRONTEND_PATH=!FRONTEND_PATH:\=/!"

copy "%DEPLOY_ROOT%config\nginx-clean.conf" "%DEPLOY_ROOT%frontend\nginx-1.28.0\conf\nginx.conf" >nul
powershell -Command "(Get-Content '%DEPLOY_ROOT%frontend\nginx-1.28.0\conf\nginx.conf') -replace '{{FRONTEND_PATH}}', '!FRONTEND_PATH!' | Set-Content '%DEPLOY_ROOT%frontend\nginx-1.28.0\conf\nginx.conf'"

if exist "%DEPLOY_ROOT%frontend\nginx-1.28.0\conf\nginx.conf" (
    echo 成功: Nginx配置已生成
) else (
    echo 错误: Nginx配置生成失败
    pause
    exit /b 1
)

:: Start Nginx
echo.
echo [4/4] 启动Nginx...
cd /d "%DEPLOY_ROOT%frontend\nginx-1.28.0"

:: Stop existing Nginx
nginx.exe -s quit >nul 2>&1

:: Start Nginx
start "Nginx Server" nginx.exe

:: Wait and check
timeout /t 3 >nul
netstat -an | findstr ":80 " >nul 2>&1
if not errorlevel 1 (
    echo 成功: 前端服务已启动
    echo.
    echo 前端地址: http://localhost
    echo 默认登录: admin / admin123
    echo.
    echo 正在打开浏览器...
    start http://localhost
) else (
    echo 错误: 前端服务启动失败
    echo 检查日志: logs\error.log
)

echo.
pause
