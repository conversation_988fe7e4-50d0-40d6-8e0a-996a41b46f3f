@echo off
setlocal enabledelayedexpansion

:: ===========================================
:: 若依Vue Pro - 前端服务启动脚本
:: 支持双击启动，自动配置Nginx
:: ===========================================

title Yudao Vue Pro - Frontend Service

echo.
echo ========================================
echo   Yudao Vue Pro - Frontend Service
echo ========================================
echo.

:: Get script directory
set "SCRIPT_DIR=%~dp0"
set "DEPLOY_ROOT=%SCRIPT_DIR%"

echo [INFO] Deploy directory: %DEPLOY_ROOT%
echo.

:: Environment check
echo [1/6] Checking Nginx program...
if not exist "%DEPLOY_ROOT%frontend\nginx-1.28.0\nginx.exe" (
    echo [错误] Nginx程序未找到
    echo [路径] %DEPLOY_ROOT%frontend\nginx-1.28.0\nginx.exe
    echo [提示] 请确保Nginx已正确放置
    echo.
    pause
    exit /b 1
) else (
    echo [成功] Nginx程序检测成功
)

:: 检查前端文件
echo.
echo [2/6] 检查前端静态文件...
if not exist "%DEPLOY_ROOT%frontend\dist\index.html" (
    echo [错误] 前端静态文件未找到
    echo [路径] %DEPLOY_ROOT%frontend\dist\index.html
    echo [提示] 请确保前端已完成构建
    echo.
    pause
    exit /b 1
) else (
    echo [成功] 前端静态文件检测成功
)

:: 检查配置模板
echo.
echo [3/6] 检查Nginx配置模板...
if not exist "%DEPLOY_ROOT%config\nginx.conf" (
    echo [错误] Nginx配置模板未找到
    echo [路径] %DEPLOY_ROOT%config\nginx.conf
    echo.
    pause
    exit /b 1
) else (
    echo [成功] Nginx配置模板检测成功
)

:: 生成Nginx配置
echo.
echo [4/6] 生成Nginx配置文件...
set "FRONTEND_PATH=%DEPLOY_ROOT%frontend\dist"
set "FRONTEND_PATH=!FRONTEND_PATH:\=/!"

:: 使用nginx-clean.conf模板，与simple版本保持一致
copy "%DEPLOY_ROOT%config\nginx-clean.conf" "%DEPLOY_ROOT%frontend\nginx-1.28.0\conf\nginx.conf" >nul 2>&1
if errorlevel 1 (
    echo [错误] 无法复制Nginx配置模板
    pause
    exit /b 1
)

:: 使用PowerShell进行模板替换（与simple版本保持一致）
powershell -Command "(Get-Content '%DEPLOY_ROOT%frontend\nginx-1.28.0\conf\nginx.conf') -replace '{{FRONTEND_PATH}}', '!FRONTEND_PATH!' | Set-Content '%DEPLOY_ROOT%frontend\nginx-1.28.0\conf\nginx.conf'"

if exist "%DEPLOY_ROOT%frontend\nginx-1.28.0\conf\nginx.conf" (
    echo [成功] Nginx配置文件生成成功
) else (
    echo [错误] Nginx配置文件生成失败
    pause
    exit /b 1
)

:: 检查端口占用
echo.
echo [5/6] 检查端口占用...
netstat -an | findstr ":80 " >nul 2>&1
if not errorlevel 1 (
    echo [警告] 端口80已被占用
    echo [提示] 可能是IIS或其他Web服务器占用
    echo [建议] 停止其他Web服务器或修改Nginx端口
    echo.
    set /p CONTINUE="是否继续启动? (y/N): "
    if /i not "!CONTINUE!"=="y" (
        echo [取消] 用户取消启动
        pause
        exit /b 1
    )
) else (
    echo [成功] 端口80可用
)

:: 启动Nginx
echo.
echo [6/6] 启动前端服务...
echo ========================================
echo   Nginx启动中...
echo ========================================
echo.

cd /d "%DEPLOY_ROOT%frontend\nginx-1.28.0"

:: 先停止可能存在的Nginx进程
nginx.exe -s quit >nul 2>&1

:: 启动Nginx
start "Nginx Server" nginx.exe

:: 等待启动
timeout /t 2 >nul

:: 检查启动状态
netstat -an | findstr ":80 " >nul 2>&1
if not errorlevel 1 (
    echo [成功] 前端服务启动成功！
    echo.
    echo ========================================
    echo   访问信息
    echo ========================================
    echo [前端地址] http://localhost
    echo [默认账号] admin / admin123
    echo.
    echo [提示] 请确保后端服务也已启动
    echo [停止服务] 运行: nginx -s quit
    echo.
    echo 浏览器将自动打开...
    start http://localhost
) else (
    echo [错误] 前端服务启动失败
    echo [提示] 请检查错误日志: logs\error.log
    echo.
)

pause
